package config

import (
	"bufio"
	"fmt"
	"os"
	"strings"
)

// Config holds the application configuration
type Config struct {
	JiraAPIToken string
}

// GlobalConfig is the global configuration instance
var GlobalConfig *Config

// LoadConfig loads configuration from .env file and environment variables
func LoadConfig() error {
	config := &Config{}
	
	// Try to load from .env file first
	err := loadFromEnvFile(config, ".env")
	if err != nil {
		fmt.Printf("Warning: Could not load .env file: %v\n", err)
	}
	
	// Override with actual environment variables if they exist
	if token := os.Getenv("JIRA_API_TOKEN"); token != "" {
		config.JiraAPIToken = token
	}
	
	// Validate required configuration
	if config.JiraAPIToken == "" {
		return fmt.Errorf("JIRA_API_TOKEN is required but not set")
	}
	
	GlobalConfig = config
	return nil
}

// loadFromEnvFile loads configuration from a .env file
func loadFromEnvFile(config *Config, filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		
		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		// Parse key=value or key = "value" format
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}
		
		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		
		// Remove quotes if present
		if len(value) >= 2 && ((value[0] == '"' && value[len(value)-1] == '"') || 
			(value[0] == '\'' && value[len(value)-1] == '\'')) {
			value = value[1 : len(value)-1]
		}
		
		// Set configuration values based on key
		switch key {
		case "JiraAPIKey", "JIRA_API_TOKEN":
			config.JiraAPIToken = value
		}
	}
	
	return scanner.Err()
}

// GetJiraAPIToken returns the Jira API token
func GetJiraAPIToken() string {
	if GlobalConfig == nil {
		return ""
	}
	return GlobalConfig.JiraAPIToken
}
