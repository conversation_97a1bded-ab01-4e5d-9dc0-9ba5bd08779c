package main

import (
	"fmt"
	"slices"
)

func main() {
	var s[] string
	// s is not init (so it's nil)
	fmt.Println("uninit:", s, s == nil, len(s) == 0)

	// make a slice with length 3. 
	// slice grows like vector or arraylist
	s = make([]string, 3)
    fmt.Println("emp:", s, "len:", len(s), "cap:", cap(s))

	s = append(s, "Da")

	// copy slice
	c := make([]string, len(s))
	copy(c, s)

	// start at index 2 and end at index 4
	// basically range [2,5) of s
	lesChats1 := s[2:5]
	lesChats2 := s[2:]
	lesChats3 := s[:5]

	if slices.Equal(lesChats1, lesChats2) {
		// pass
	}

	twoD := make([][]int, 3)

}