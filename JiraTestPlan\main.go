package main

import (
	"JiraTestPlan/config"
	"JiraTestPlan/request"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"
)

// Global API token - loaded from config
var apiToken string

// JiraResponse represents the structure of Jira API response
type JiraResponse struct {
	Issues []struct {
		Key    string `json:"key"`
		Fields struct {
			Summary        string `json:"summary"`
			ResolutionDate string `json:"resolutiondate"`
			Status         struct {
				Name string `json:"name"`
			} `json:"status"`
		} `json:"fields"`
	} `json:"issues"`
}

// TestInfo holds detailed information about a test
type TestInfo struct {
	Key        string
	Summary    string
	FinishedOn string
	Status     string
	Execution  string
}

// TestResult holds the result of getting tests for an execution
type TestResult struct {
	Execution string
	Tests     []TestInfo
	Error     error
}

func getExecutions(testPlan string) ([]string, error) {
	endpoint := "api/2/search"

	params := map[string]string{
		"jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
		"startAt":    "0",
		"maxResults": "500",
		"fields":     "key,summary",
	}

	responseData, err := request.MakeRequestOptimized(apiToken, endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	var jsonData JiraResponse
	err = json.Unmarshal(responseData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON: %w", err)
	}

	keys := make([]string, 0, len(jsonData.Issues))
	for _, issue := range jsonData.Issues {
		keys = append(keys, issue.Key)
	}
	return keys, nil
}

func getTests(testExecution string) ([]TestInfo, error) {
	endpoint := "api/2/search"

	params := map[string]string{
		"jql":        "issue in testExecutionTests('" + testExecution + "')",
		"startAt":    "0",
		"maxResults": "500",
		"fields":     "key,summary,resolutiondate,status",
	}

	responseData, err := request.MakeRequestOptimized(apiToken, endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	var jsonData JiraResponse
	err = json.Unmarshal(responseData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON: %w", err)
	}

	tests := make([]TestInfo, 0, len(jsonData.Issues))
	for _, issue := range jsonData.Issues {
		testInfo := TestInfo{
			Key:        issue.Key,
			Summary:    issue.Fields.Summary,
			FinishedOn: issue.Fields.ResolutionDate,
			Status:     issue.Fields.Status.Name,
			Execution:  testExecution,
		}
		tests = append(tests, testInfo)
	}
	return tests, nil
}

// getTestsConcurrently fetches tests for all executions concurrently
func getTestsConcurrently(executions []string, maxConcurrency int) []TestResult {
	results := make([]TestResult, len(executions))

	// Create a semaphore to limit concurrent requests
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup

	for i, execution := range executions {
		wg.Add(1)
		go func(index int, exec string) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			tests, err := getTests(exec)
			results[index] = TestResult{
				Execution: exec,
				Tests:     tests,
				Error:     err,
			}
		}(i, execution)
	}

	wg.Wait()
	return results
}

// exportToCSV exports test results to a CSV file
func exportToCSV(results []TestResult, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("error creating CSV file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write CSV header
	header := []string{"Test Key", "Summary", "Finished On", "Status", "Execution"}
	if err := writer.Write(header); err != nil {
		return fmt.Errorf("error writing CSV header: %w", err)
	}

	// Write test data
	for _, result := range results {
		if result.Error != nil {
			// Write error row
			errorRow := []string{
				"ERROR",
				fmt.Sprintf("Error getting tests for execution %s: %v", result.Execution, result.Error),
				"",
				"ERROR",
				result.Execution,
			}
			if err := writer.Write(errorRow); err != nil {
				return fmt.Errorf("error writing error row: %w", err)
			}
			continue
		}

		for _, test := range result.Tests {
			row := []string{
				test.Key,
				test.Summary,
				test.FinishedOn,
				test.Status,
				test.Execution,
			}
			if err := writer.Write(row); err != nil {
				return fmt.Errorf("error writing test row: %w", err)
			}
		}
	}

	return nil
}

func main() {
	// Load configuration from .env file
	err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Error loading configuration: %v\n", err)
		return
	}

	// Set the global API token and get test plan key
	apiToken = config.GetJiraAPIToken()
	testPlanKey := config.GetTestPlanKey()
	fmt.Printf("Loaded API token: %s...\n", apiToken[:10]) // Show first 10 chars for verification
	fmt.Printf("Using test plan key: %s\n", testPlanKey)

	// Start timing
	startTime := time.Now()

	fmt.Println("Fetching executions...")
	executions, err := getExecutions(testPlanKey)
	if err != nil {
		fmt.Printf("Error getting executions: %v\n", err)
		return
	}
	fmt.Printf("Found %d executions\n", len(executions))

	if len(executions) == 0 {
		fmt.Println("No executions found")
		return
	}

	// Fetch tests concurrently with a reasonable limit
	maxConcurrency := 10 // Adjust based on API rate limits
	fmt.Printf("Fetching tests for all executions concurrently (max %d concurrent requests)...\n", maxConcurrency)

	results := getTestsConcurrently(executions, maxConcurrency)

	// Display results
	totalTests := 0
	for _, result := range results {
		if result.Error != nil {
			fmt.Printf("Error getting tests for execution %s: %v\n", result.Execution, result.Error)
		} else {
			fmt.Printf("Found %d tests for execution %s\n", len(result.Tests), result.Execution)
			totalTests += len(result.Tests)
		}
	}

	elapsed := time.Since(startTime)
	fmt.Printf("\nTotal: %d tests across %d executions\n", totalTests, len(executions))
	fmt.Printf("Completed in: %v\n", elapsed)

	// Export to CSV
	csvFilename := "Xporter.csv"
	fmt.Printf("\nExporting results to %s...\n", csvFilename)
	err = exportToCSV(results, csvFilename)
	if err != nil {
		fmt.Printf("Error exporting to CSV: %v\n", err)
		return
	}
	fmt.Printf("Successfully exported %d tests to %s\n", totalTests, csvFilename)
}
