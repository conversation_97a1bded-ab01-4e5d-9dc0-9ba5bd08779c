package main

import (
	"JiraTestPlan/request"
	"bytes"
	"encoding/json"
	"fmt"
)

var api = "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"

func getExecutions(testPlan string) []string {
    endpoint := "api/2/search"

    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    var buffer bytes.Buffer
    err := request.MakeRequest(api, endpoint, params, &buffer)
    if err != nil {
        fmt.Printf("Error making request: %v\n", err)
        return nil
    }

    responseData := buffer.String()

    // Parse JSON response
    var jsonData struct { Issues []struct { Key string } } 
    err = json.Unmarshal([]byte(responseData), &jsonData)
    if err != nil {
        fmt.Printf("Error parsing JSON: %v\n", err)
        return nil
    }

    var keys []string
    for _, issue := range jsonData.Issues {
        keys = append(keys, issue.Key)
    }
    return keys
}

func getTests(testExecution string) []string {
    endpoint := "api/2/search"

    params := map[string]string{
        "jql":        "issue in testExecutionTests('" + testExecution + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    var buffer bytes.Buffer
    err := request.MakeRequest(api, endpoint, params, &buffer)
    if err != nil {
        fmt.Printf("Error making request: %v\n", err)
        return nil
    }

    responseData := buffer.String()

    // Parse JSON response
    var jsonData struct { Issues []struct { Key string } } 
    err = json.Unmarshal([]byte(responseData), &jsonData)
    if err != nil {
        fmt.Printf("Error parsing JSON: %v\n", err)
        return nil
    }

    var keys []string
    for _, issue := range jsonData.Issues {
        keys = append(keys, issue.Key)
    }
    return keys
}

func main() {
    issueKey := "DNS300-1391"
    executions := getExecutions(issueKey)
    fmt.Printf("Found %d executions\n", len(executions))

    for _, execution := range executions {
        tests := getTests(execution)
        fmt.Printf("Found %d tests for execution %s\n", len(tests), execution)
    }

}
