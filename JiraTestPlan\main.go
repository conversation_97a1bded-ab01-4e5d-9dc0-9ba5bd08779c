package main

import (
	"JiraTestPlan/config"
	"JiraTestPlan/request"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// Global API token - loaded from config
var apiToken string

// JiraResponse represents the structure of Jira API response
type JiraResponse struct {
	Issues []struct {
		Key string `json:"key"`
	} `json:"issues"`
}

// TestResult holds the result of getting tests for an execution
type TestResult struct {
	Execution string
	Tests     []string
	Error     error
}

func getExecutions(testPlan string) ([]string, error) {
	endpoint := "api/2/search"

	params := map[string]string{
		"jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
		"startAt":    "0",
		"maxResults": "500",
		"fields":     "key",
	}

	responseData, err := request.MakeRequestOptimized(apiToken, endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	var jsonData JiraResponse
	err = json.Unmarshal(responseData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON: %w", err)
	}

	keys := make([]string, 0, len(jsonData.Issues))
	for _, issue := range jsonData.Issues {
		keys = append(keys, issue.Key)
	}
	return keys, nil
}

func getTests(testExecution string) ([]string, error) {
	endpoint := "api/2/search"

	params := map[string]string{
		"jql":        "issue in testExecutionTests('" + testExecution + "')",
		"startAt":    "0",
		"maxResults": "500",
		"fields":     "key",
	}

	responseData, err := request.MakeRequestOptimized(apiToken, endpoint, params)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}

	var jsonData JiraResponse
	err = json.Unmarshal(responseData, &jsonData)
	if err != nil {
		return nil, fmt.Errorf("error parsing JSON: %w", err)
	}

	keys := make([]string, 0, len(jsonData.Issues))
	for _, issue := range jsonData.Issues {
		keys = append(keys, issue.Key)
	}
	return keys, nil
}

// getTestsConcurrently fetches tests for all executions concurrently
func getTestsConcurrently(executions []string, maxConcurrency int) []TestResult {
	results := make([]TestResult, len(executions))

	// Create a semaphore to limit concurrent requests
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup

	for i, execution := range executions {
		wg.Add(1)
		go func(index int, exec string) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			tests, err := getTests(exec)
			results[index] = TestResult{
				Execution: exec,
				Tests:     tests,
				Error:     err,
			}
		}(i, execution)
	}

	wg.Wait()
	return results
}

func main() {
	// Load configuration from .env file
	err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Error loading configuration: %v\n", err)
		return
	}

	// Set the global API token
	apiToken = config.GetJiraAPIToken()
	fmt.Printf("Loaded API token: %s...\n", apiToken[:10]) // Show first 10 chars for verification

	issueKey := "DNS300-1391"

	// Start timing
	startTime := time.Now()

	fmt.Println("Fetching executions...")
	executions, err := getExecutions(issueKey)
	if err != nil {
		fmt.Printf("Error getting executions: %v\n", err)
		return
	}
	fmt.Printf("Found %d executions\n", len(executions))

	if len(executions) == 0 {
		fmt.Println("No executions found")
		return
	}

	// Fetch tests concurrently with a reasonable limit
	maxConcurrency := 10 // Adjust based on API rate limits
	fmt.Printf("Fetching tests for all executions concurrently (max %d concurrent requests)...\n", maxConcurrency)

	results := getTestsConcurrently(executions, maxConcurrency)

	// Display results
	totalTests := 0
	for _, result := range results {
		if result.Error != nil {
			fmt.Printf("Error getting tests for execution %s: %v\n", result.Execution, result.Error)
		} else {
			fmt.Printf("Found %d tests for execution %s\n", len(result.Tests), result.Execution)
			totalTests += len(result.Tests)
		}
	}

	elapsed := time.Since(startTime)
	fmt.Printf("\nTotal: %d tests across %d executions\n", totalTests, len(executions))
	fmt.Printf("Completed in: %v\n", elapsed)
}
