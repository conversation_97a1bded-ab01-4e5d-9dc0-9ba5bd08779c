package main

import "fmt"

func plus(a int, b int) int {
	return a + b
} 

// return 2 values
func minus(a int, b int) (int, int) {
	return a-b, b-a
}

// function takes unlimited params
// no return
func sum( nums ...int) {
	//assume nums []int
	fmt.Print(nums, " ")
	fmt.Println(len(nums))

	// for each loop. 
	// the is the variable that holds the index
	for _, num := range nums {
        total += num
    }

}

func main() {
	res := plus(1,2)
	re, te := minus(1,2)

	sum(1, 2)
    sum(1, 2, 3)
}
