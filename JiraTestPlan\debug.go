package main

import (
	"JiraTestPlan/config"
	"JiraTestPlan/request"
	"encoding/json"
	"fmt"
)

func main() {
	// Load configuration
	err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Error loading configuration: %v\n", err)
		return
	}

	apiToken := config.GetJiraAPIToken()
	testPlanKey := config.GetTestPlanKey()

	// Get first execution to debug
	fmt.Println("Getting executions...")
	endpoint := "api/2/search"
	params := map[string]string{
		"jql":        "issue in testPlanTestExecutions('" + testPlanKey + "')",
		"startAt":    "0",
		"maxResults": "1",
		"fields":     "key,summary",
	}

	responseData, err := request.MakeRequestOptimized(apiToken, endpoint, params)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Println("Executions response:")
	fmt.Println(string(responseData))

	// Parse to get first execution
	var execResponse struct {
		Issues []struct {
			Key string `json:"key"`
		} `json:"issues"`
	}
	
	err = json.Unmarshal(responseData, &execResponse)
	if err != nil {
		fmt.Printf("Error parsing: %v\n", err)
		return
	}

	if len(execResponse.Issues) == 0 {
		fmt.Println("No executions found")
		return
	}

	firstExecution := execResponse.Issues[0].Key
	fmt.Printf("\nGetting tests for execution: %s\n", firstExecution)

	// Get tests with all available fields
	params = map[string]string{
		"jql":        "issue in testExecutionTests('" + firstExecution + "')",
		"startAt":    "0",
		"maxResults": "2",
		"fields":     "*all",
	}

	responseData, err = request.MakeRequestOptimized(apiToken, endpoint, params)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Println("\nTests response (first 2 tests with all fields):")
	
	// Pretty print JSON
	var prettyJSON interface{}
	err = json.Unmarshal(responseData, &prettyJSON)
	if err != nil {
		fmt.Printf("Error parsing JSON: %v\n", err)
		return
	}
	
	prettyData, err := json.MarshalIndent(prettyJSON, "", "  ")
	if err != nil {
		fmt.Printf("Error formatting JSON: %v\n", err)
		return
	}
	
	fmt.Println(string(prettyData))
}
